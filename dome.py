# from flask import Flask
# app = Flask(__name__)
#
# @app.route('/')
# def index():
#     return '<h1>Hello world<h1>'
#
# app.run()

# 路由
# from flask import Flask
# app = Flask(__name__)
#
# @app.route('/hello', methods=['GET', 'POST'],endpoint='hello')
# def hello():
#     return 'Hello World!'
#
# @app.route('/hi', methods=['GET', 'POST'],endpoint='hi')
# def hi():
#     return 'hi hi'
#
# if __name__ == '__main__':
#     app.run(debug=True)

# from flask import Flask
#
# app = Flask(__name__)
#
#
# @app.route('/user/<id>')
# def index():
#     if id == '1':
#         return 'python'
#     if id == str(2):
#         return 'django'
#     if int(id) == 3:
#         return 'kevin.wei'
#     return 'hello'
#
#
# if __name__ == '__main__':
#     app.run(debug=True)

# import turtle
#
# turtle.pensize(4)
# turtle.pencolor('red')
#
# turtle.forward(100)
# turtle.right(90)
# turtle.forward(100)
# turtle.right(90)
# turtle.forward(100)
# turtle.right(90)
# turtle.forward(100)

# turtle.mainloop()


# import turtle as t
#
# # 绘制小猪佩奇
# # =======================================
#
# t.pensize(4)
# t.hideturtle()
# t.colormode(255)
# t.color((255, 155, 192), "pink")
# t.setup(840, 500)
# t.speed(50)
#
# # 鼻子
# t.pu()
# t.goto(-100, 100)
# t.pd()
# t.seth(-30)
# t.begin_fill()
# a = 0.4
# for i in range(120):
#     if 0 <= i < 30 or 60 <= i < 90:
#         a = a + 0.08
#         t.lt(3)  # 向左转3度
#         t.fd(a)  # 向前走a的步长
#     else:
#         a = a - 0.08
#         t.lt(3)
#         t.fd(a)
#         t.end_fill()
#
# t.pu()
# t.seth(90)
# t.fd(25)
# t.seth(0)
# t.fd(10)
# t.pd()
# t.pencolor(255, 155, 192)
# t.seth(10)
# t.begin_fill()
# t.circle(5)
# t.color(160, 82, 45)
# t.end_fill()
#
# t.pu()
# t.seth(0)
# t.fd(20)
# t.pd()
# t.pencolor(255, 155, 192)
# t.seth(10)
# t.begin_fill()
# t.circle(5)
# t.color(160, 82, 45)
# t.end_fill()
#
# # 头
# t.color((255, 155, 192), "pink")
# t.pu()
# t.seth(90)
# t.fd(41)
# t.seth(0)
# t.fd(0)
# t.pd()
# t.begin_fill()
# t.seth(180)
# t.circle(300, -30)
# t.circle(100, -60)
# t.circle(80, -100)
# t.circle(150, -20)
# t.circle(60, -95)
# t.seth(161)
# t.circle(-300, 15)
# t.pu()
# t.goto(-100, 100)
# t.pd()
# t.seth(-30)
# a = 0.4
# for i in range(60):
#     if 0 <= i < 30 or 60 <= i < 90:
#         a = a + 0.08
#         t.lt(3)  # 向左转3度
#         t.fd(a)  # 向前走a的步长
#     else:
#         a = a - 0.08
#         t.lt(3)
#         t.fd(a)
#         t.end_fill()
#
# # 耳朵
# t.color((255, 155, 192), "pink")
# t.pu()
# t.seth(90)
# t.fd(-7)
# t.seth(0)
# t.fd(70)
# t.pd()
# t.begin_fill()
# t.seth(100)
# t.circle(-50, 50)
# t.circle(-10, 120)
# t.circle(-50, 54)
# t.end_fill()
#
# t.pu()
# t.seth(90)
# t.fd(-12)
# t.seth(0)
# t.fd(30)
# t.pd()
# t.begin_fill()
# t.seth(100)
# t.circle(-50, 50)
# t.circle(-10, 120)
# t.circle(-50, 56)
# t.end_fill()
#
# # 眼睛
# t.color((255, 155, 192), "white")
# t.pu()
# t.seth(90)
# t.fd(-20)
# t.seth(0)
# t.fd(-95)
# t.pd()
# t.begin_fill()
# t.circle(15)
# t.end_fill()
#
# t.color("black")
# t.pu()
# t.seth(90)
# t.fd(12)
# t.seth(0)
# t.fd(-3)
# t.pd()
# t.begin_fill()
# t.circle(3)
# t.end_fill()
#
# t.color((255, 155, 192), "white")
# t.pu()
# t.seth(90)
# t.fd(-25)
# t.seth(0)
# t.fd(40)
# t.pd()
# t.begin_fill()
# t.circle(15)
# t.end_fill()
#
# t.color("black")
# t.pu()
# t.seth(90)
# t.fd(12)
# t.seth(0)
# t.fd(-3)
# t.pd()
# t.begin_fill()
# t.circle(3)
# t.end_fill()
#
# # 腮
# t.color((255, 155, 192))
# t.pu()
# t.seth(90)
# t.fd(-95)
# t.seth(0)
# t.fd(65)
# t.pd()
# t.begin_fill()
# t.circle(30)
# t.end_fill()
#
# # 嘴
# t.color(239, 69, 19)
# t.pu()
# t.seth(90)
# t.fd(15)
# t.seth(0)
# t.fd(-100)
# t.pd()
# t.seth(-80)
# t.circle(30, 40)
# t.circle(40, 80)
#
# # 身体
# t.color("red", (255, 99, 71))
# t.pu()
# t.seth(90)
# t.fd(-20)
# t.seth(0)
# t.fd(-78)
# t.pd()
# t.begin_fill()
# t.seth(-130)
# t.circle(100, 10)
# t.circle(300, 30)
# t.seth(0)
# t.fd(230)
# t.seth(90)
# t.circle(300, 30)
# t.circle(100, 3)
# t.color((255, 155, 192), (255, 100, 100))
# t.seth(-135)
# t.circle(-80, 63)
# t.circle(-150, 24)
# t.end_fill()
#
# # 手
# t.color((255, 155, 192))
# t.pu()
# t.seth(90)
# t.fd(-40)
# t.seth(0)
# t.fd(-27)
# t.pd()
# t.seth(-160)
# t.circle(300, 15)
# t.pu()
# t.seth(90)
# t.fd(15)
# t.seth(0)
# t.fd(0)
# t.pd()
# t.seth(-10)
# t.circle(-20, 90)
#
# t.pu()
# t.seth(90)
# t.fd(30)
# t.seth(0)
# t.fd(237)
# t.pd()
# t.seth(-20)
# t.circle(-300, 15)
# t.pu()
# t.seth(90)
# t.fd(20)
# t.seth(0)
# t.fd(0)
# t.pd()
# t.seth(-170)
# t.circle(20, 90)
#
# # 脚
# t.pensize(10)
# t.color((240, 128, 128))
# t.pu()
# t.seth(90)
# t.fd(-75)
# t.seth(0)
# t.fd(-180)
# t.pd()
# t.seth(-90)
# t.fd(40)
# t.seth(-180)
# t.color("black")
# t.pensize(15)
# t.fd(20)
#
# t.pensize(10)
# t.color((240, 128, 128))
# t.pu()
# t.seth(90)
# t.fd(40)
# t.seth(0)
# t.fd(90)
# t.pd()
# t.seth(-90)
# t.fd(40)
# t.seth(-180)
# t.color("black")
# t.pensize(15)
# t.fd(20)
#
# # 尾巴
# t.pensize(4)
# t.color((255, 155, 192))
# t.pu()
# t.seth(90)
# t.fd(70)
# t.seth(0)
# t.fd(95)
# t.pd()
# t.seth(0)
# t.circle(70, 20)
# t.circle(10, 330)
# t.circle(70, 30)
# t.done()


import turtle as t
# # 露比
# t.colormode(255)
# t.speed(0)
# t.setup(614.4, 597.6)
# t.bgcolor('white')
# t.pencolor(173, 120, 117)
# t.fillcolor(173, 120, 117)
# t.pensize(2)
# t.begin_fill()
# t.penup()
# t.goto(-97, 167)
# t.pendown()
# t.goto(-97, 180)
# t.goto(-97, 189)
# t.goto(-94, 193)
# t.goto(-89, 204)
# t.goto(-84, 213)
# t.goto(-76, 223)
# t.goto(-73, 226)
# t.goto(-70, 226)
# t.goto(-69, 222)
# t.goto(-78, 208)
# t.goto(-83, 198)
# t.goto(-89, 184)
# t.goto(-94, 175)
# t.goto(-97, 167)
# t.end_fill()
# t.pencolor(171, 71, 73)
# t.fillcolor(171, 71, 73)
# t.begin_fill()
# t.penup()
# t.goto(-99, 168)
# t.pendown()
# t.goto(-102, 201)
# t.goto(-102, 219)
# t.goto(-101, 237)
# t.goto(-99, 245)
# t.goto(-100, 255)
# t.goto(-102, 259)
# t.goto(-105, 259)
# t.goto(-108, 256)
# t.goto(-111, 251)
# t.goto(-111, 246)
# t.goto(-109, 208)
# t.goto(-108, 199)
# t.goto(-108, 186)
# t.goto(-105, 162)
# t.goto(-99, 168)
# t.end_fill()
# t.pencolor(234, 137, 148)
# t.fillcolor(234, 137, 148)
# t.begin_fill()
# t.penup()
# t.goto(223, -51)
# t.pendown()
# t.goto(222, -31)
# t.goto(217, -17)
# t.goto(212, -2)
# t.goto(205, 8)
# t.goto(193, 27)
# t.goto(186, 32)
# t.goto(176, 43)
# t.goto(165, 56)
# t.goto(151, 72)
# t.goto(140, 90)
# t.goto(130, 105)
# t.goto(119, 122)
# t.goto(111, 135)
# t.goto(105, 139)
# t.goto(105, 144)
# t.goto(106, 148)
# t.goto(119, 160)
# t.goto(123, 167)
# t.goto(123, 172)
# t.goto(120, 177)
# t.goto(117, 182)
# t.goto(109, 186)
# t.goto(103, 188)
# t.goto(94, 189)
# t.goto(85, 188)
# t.goto(75, 185)
# t.goto(67, 181)
# t.goto(60, 175)
# t.goto(46, 179)
# t.goto(31, 183)
# t.goto(16, 186)
# t.goto(3, 184)
# t.goto(-11, 185)
# t.goto(-24, 184)
# t.goto(-41, 182)
# t.goto(-57, 178)
# t.goto(-73, 175)
# t.goto(-96, 167)
# t.goto(-111, 159)
# t.goto(-124, 150)
# t.goto(-138, 141)
# t.goto(-150, 129)
# t.goto(-166, 112)
# t.goto(-177, 98)
# t.goto(-185, 87)
# t.goto(-188, 80)
# t.goto(-190, 73)
# t.goto(-201, 75)
# t.goto(-210, 73)
# t.goto(-217, 73)
# t.goto(-224, 69)
# t.goto(-229, 64)
# t.goto(-231, 58)
# t.goto(-232, 52)
# t.goto(-231, 43)
# t.goto(-226, 36)
# t.goto(-222, 31)
# t.goto(-215, 25)
# t.goto(-208, 22)
# t.goto(-193, 19)
# t.goto(-188, 0)
# t.goto(-183, -13)
# t.goto(-180, -27)
# t.goto(-174, -41)
# t.goto(-169, -55)
# t.goto(-164, -73)
# t.goto(-160, -85)
# t.goto(-157, -91)
# t.goto(-157, -100)
# t.goto(-154, -107)
# t.goto(-151, -119)
# t.goto(-144, -140)
# t.goto(-132, -145)
# t.goto(-124, -148)
# t.goto(-117, -152)
# t.goto(-111, -158)
# t.goto(-106, -169)
# t.goto(-101, -181)
# t.goto(-90, -186)
# t.goto(-55, -195)
# t.goto(-32, -198)
# t.goto(-7, -199)
# t.goto(15, -202)
# t.goto(34, -203)
# t.goto(55, -204)
# t.goto(70, -202)
# t.goto(77, -201)
# t.goto(90, -196)
# t.goto(105, -189)
# t.goto(124, -178)
# t.goto(142, -165)
# t.goto(162, -150)
# t.goto(178, -138)
# t.goto(192, -124)
# t.goto(203, -109)
# t.goto(213, -93)
# t.goto(220, -72)
# t.goto(225, -57)
# t.goto(223, -51)
# t.end_fill()
# t.pencolor(122, 85, 77)
# t.fillcolor(169, 160, 157)
# t.pensize(6)
# t.begin_fill()
# t.penup()
# t.goto(-196, 39)
# t.pendown()
# t.goto(-194, 50)
# t.goto(-195, 57)
# t.goto(-199, 61)
# t.goto(-205, 63)
# t.goto(-212, 63)
# t.goto(-217, 60)
# t.goto(-220, 56)
# t.goto(-221, 51)
# t.goto(-219, 44)
# t.goto(-217, 41)
# t.goto(-213, 37)
# t.goto(-208, 34)
# t.goto(-203, 34)
# t.goto(-199, 37)
# t.goto(-196, 39)
# t.end_fill()
# t.begin_fill()
# t.penup()
# t.goto(96, 152)
# t.pendown()
# t.goto(83, 159)
# t.goto(73, 166)
# t.goto(74, 169)
# t.goto(76, 172)
# t.goto(81, 174)
# t.goto(87, 178)
# t.goto(93, 179)
# t.goto(98, 179)
# t.goto(103, 177)
# t.goto(108, 175)
# t.goto(111, 172)
# t.goto(111, 166)
# t.goto(105, 161)
# t.goto(96, 152)
# t.end_fill()
# t.pencolor(30, 4, 0)
# t.fillcolor(30, 4, 0)
# t.pensize(2)
# t.begin_fill()
# t.penup()
# t.goto(-29, -25)
# t.pendown()
# t.goto(-36, -41)
# t.goto(-41, -46)
# t.goto(-45, -48)
# t.goto(-50, -49)
# t.goto(-57, -48)
# t.goto(-76, -45)
# t.goto(-79, -42)
# t.goto(-79, -39)
# t.goto(-77, -37)
# t.goto(-73, -37)
# t.goto(-51, -40)
# t.goto(-49, -39)
# t.goto(-44, -33)
# t.goto(-37, -24)
# t.goto(-34, -22)
# t.goto(-31, -22)
# t.goto(-29, -25)
# t.end_fill()
# t.begin_fill()
# t.penup()
# t.goto(97, 21)
# t.pendown()
# t.goto(86, 0)
# t.goto(81, 0)
# t.goto(66, 0)
# t.goto(52, 3)
# t.goto(48, 5)
# t.goto(47, 7)
# t.goto(49, 12)
# t.goto(51, 13)
# t.goto(56, 12)
# t.goto(63, 9)
# t.goto(69, 8)
# t.goto(79, 8)
# t.goto(82, 12)
# t.goto(87, 21)
# t.goto(90, 24)
# t.goto(94, 26)
# t.goto(96, 24)
# t.goto(97, 21)
# t.end_fill()
# t.pencolor(81, 4, 56)
# t.fillcolor(81, 4, 56)
# t.begin_fill()
# t.penup()
# t.goto(94, -73)
# t.pendown()
# t.goto(87, -82)
# t.goto(79, -87)
# t.goto(72, -91)
# t.goto(63, -93)
# t.goto(51, -94)
# t.goto(36, -92)
# t.goto(25, -90)
# t.goto(17, -86)
# t.goto(7, -81)
# t.goto(0, -73)
# t.goto(-4, -63)
# t.goto(-3, -56)
# t.goto(0, -50)
# t.goto(4, -46)
# t.goto(10, -42)
# t.goto(19, -37)
# t.goto(27, -36)
# t.goto(36, -33)
# t.goto(45, -32)
# t.goto(53, -31)
# t.goto(61, -32)
# t.goto(67, -34)
# t.goto(76, -36)
# t.goto(81, -40)
# t.goto(88, -45)
# t.goto(92, -50)
# t.goto(95, -56)
# t.goto(96, -64)
# t.goto(94, -73)
# t.end_fill()
# t.pencolor(212, 169, 197)
# t.fillcolor(212, 169, 197)
# t.begin_fill()
# t.penup()
# t.goto(72, -55)
# t.pendown()
# t.goto(64, -54)
# t.goto(59, -53)
# t.goto(56, -49)
# t.goto(55, -46)
# t.goto(56, -43)
# t.goto(59, -43)
# t.goto(65, -42)
# t.goto(69, -43)
# t.goto(73, -45)
# t.goto(76, -47)
# t.goto(78, -51)
# t.goto(75, -54)
# t.goto(72, -55)
# t.end_fill()
# t.pencolor(55, 1, 0)
# t.fillcolor(55, 1, 0)
# t.begin_fill()
# t.penup()
# t.goto(143, -139)
# t.pendown()
# t.goto(142, -136)
# t.goto(139, -132)
# t.goto(136, -131)
# t.goto(130, -129)
# t.goto(122, -127)
# t.goto(115, -124)
# t.goto(110, -125)
# t.goto(108, -126)
# t.goto(107, -128)
# t.goto(78, -142)
# t.goto(76, -141)
# t.goto(75, -142)
# t.goto(73, -144)
# t.goto(67, -153)
# t.goto(60, -164)
# t.goto(58, -169)
# t.goto(57, -174)
# t.goto(58, -177)
# t.goto(62, -176)
# t.goto(67, -174)
# t.goto(72, -168)
# t.goto(75, -159)
# t.goto(77, -154)
# t.goto(82, -153)
# t.goto(117, -139)
# t.goto(121, -136)
# t.goto(124, -137)
# t.goto(128, -139)
# t.goto(132, -141)
# t.goto(136, -141)
# t.goto(141, -141)
# t.goto(143, -139)
# t.end_fill()
# t.pencolor('white')
# t.fillcolor('white')
# t.begin_fill()
# t.penup()
# t.goto(108, -133)
# t.pendown()
# t.goto(81, -145)
# t.goto(79, -145)
# t.goto(78, -144)
# t.goto(78, -141)
# t.goto(80, -138)
# t.goto(93, -131)
# t.goto(97, -129)
# t.goto(102, -128)
# t.goto(106, -127)
# t.goto(108, -130)
# t.goto(108, -132)
# t.goto(108, -133)
# t.end_fill()
# t.pencolor(211, 110, 120)
# t.fillcolor(211, 110, 120)
# t.begin_fill()
# t.penup()
# t.goto(145, -121)
# t.pendown()
# t.goto(142, -112)
# t.goto(140, -105)
# t.goto(136, -95)
# t.goto(130, -87)
# t.goto(122, -80)
# t.goto(115, -75)
# t.goto(111, -73)
# t.goto(107, -72)
# t.goto(105, -75)
# t.goto(108, -79)
# t.goto(113, -81)
# t.goto(122, -86)
# t.goto(127, -91)
# t.goto(132, -97)
# t.goto(135, -103)
# t.goto(142, -120)
# t.goto(143, -122)
# t.goto(145, -121)
# t.end_fill()
# t.begin_fill()
# t.penup()
# t.goto(145, -147)
# t.pendown()
# t.goto(142, -148)
# t.goto(136, -150)
# t.goto(127, -147)
# t.goto(120, -145)
# t.goto(114, -148)
# t.goto(102, -153)
# t.goto(86, -160)
# t.goto(82, -163)
# t.goto(81, -166)
# t.goto(79, -172)
# t.goto(75, -179)
# t.goto(78, -186)
# t.goto(81, -181)
# t.goto(87, -172)
# t.goto(90, -168)
# t.goto(97, -161)
# t.goto(103, -156)
# t.goto(112, -151)
# t.goto(116, -151)
# t.goto(123, -153)
# t.goto(129, -154)
# t.goto(135, -154)
# t.goto(141, -152)
# t.goto(144, -149)
# t.goto(145, -147)
# t.end_fill()
# t.pencolor(200, 120, 124)
# t.fillcolor(200, 120, 124)
# t.begin_fill()
# t.penup()
# t.goto(-231, -293)
# t.pendown()
# t.goto(-238, -280)
# t.goto(-243, -267)
# t.goto(-245, -255)
# t.goto(-247, -241)
# t.goto(-247, -230)
# t.goto(-245, -219)
# t.goto(-242, -210)
# t.goto(-181, -240)
# t.goto(-190, -265)
# t.goto(-194, -279)
# t.goto(-198, -293)
# t.goto(-231, -293)
# t.end_fill()
# t.pencolor(226, 131, 140)
# t.fillcolor(226, 131, 140)
# t.begin_fill()
# t.penup()
# t.goto(225, -240)
# t.pendown()
# t.goto(219, -248)
# t.goto(214, -255)
# t.goto(207, -262)
# t.goto(200, -268)
# t.goto(193, -274)
# t.goto(183, -280)
# t.goto(174, -285)
# t.goto(166, -287)
# t.goto(154, -289)
# t.goto(141, -288)
# t.goto(132, -288)
# t.goto(121, -286)
# t.goto(103, -281)
# t.goto(81, -271)
# t.goto(63, -262)
# t.goto(50, -255)
# t.goto(40, -248)
# t.goto(29, -238)
# t.goto(27, -238)
# t.goto(24, -240)
# t.goto(20, -247)
# t.goto(14, -258)
# t.goto(11, -269)
# t.goto(8, -280)
# t.goto(4, -292)
# t.goto(-197, -293)
# t.goto(-195, -286)
# t.goto(-193, -275)
# t.goto(-188, -265)
# t.goto(-184, -252)
# t.goto(-183, -242)
# t.goto(-100, -181)
# t.goto(-87, -187)
# t.goto(-75, -190)
# t.goto(-63, -193)
# t.goto(-51, -196)
# t.goto(-23, -199)
# t.goto(-10, -199)
# t.goto(7, -201)
# t.goto(21, -202)
# t.goto(36, -203)
# t.goto(49, -204)
# t.goto(60, -203)
# t.goto(71, -202)
# t.goto(73, -207)
# t.goto(77, -208)
# t.goto(88, -212)
# t.goto(105, -218)
# t.goto(115, -220)
# t.goto(125, -220)
# t.goto(133, -221)
# t.goto(142, -219)
# t.goto(151, -216)
# t.goto(157, -214)
# t.goto(166, -210)
# t.goto(181, -201)
# t.goto(198, -196)
# t.goto(204, -195)
# t.goto(220, -201)
# t.goto(222, -204)
# t.goto(229, -219)
# t.goto(229, -223)
# t.goto(225, -240)
# t.end_fill()
# t.pencolor(241, 137, 145)
# t.fillcolor(241, 137, 145)
# t.begin_fill()
# t.penup()
# t.goto(-102, -184)
# t.pendown()
# t.goto(-106, -169)
# t.goto(-111, -159)
# t.goto(-114, -155)
# t.goto(-120, -149)
# t.goto(-132, -144)
# t.goto(-144, -141)
# t.goto(-159, -150)
# t.goto(-166, -130)
# t.goto(-172, -118)
# t.goto(-179, -109)
# t.goto(-184, -105)
# t.goto(-195, -99)
# t.goto(-208, -98)
# t.goto(-216, -103)
# t.goto(-230, -109)
# t.goto(-234, -115)
# t.goto(-234, -132)
# t.goto(-233, -139)
# t.goto(-231, -146)
# t.goto(-229, -153)
# t.goto(-229, -157)
# t.goto(-231, -165)
# t.goto(-234, -172)
# t.goto(-237, -180)
# t.goto(-240, -188)
# t.goto(-241, -196)
# t.goto(-241, -204)
# t.goto(-241, -210)
# t.goto(-238, -219)
# t.goto(-234, -228)
# t.goto(-228, -233)
# t.goto(-222, -239)
# t.goto(-213, -243)
# t.goto(-203, -244)
# t.goto(-194, -244)
# t.goto(-185, -243)
# t.goto(-178, -240)
# t.goto(-168, -236)
# t.goto(-156, -229)
# t.goto(-141, -220)
# t.goto(-129, -213)
# t.goto(-115, -199)
# t.goto(-106, -190)
# t.goto(-102, -184)
# t.end_fill()
# t.pencolor(220, 200, 210)
# t.fillcolor(220, 200, 210)
# t.begin_fill()
# t.penup()
# t.goto(-177, -145)
# t.pendown()
# t.goto(-184, -148)
# t.goto(-187, -148)
# t.goto(-193, -146)
# t.goto(-205, -139)
# t.goto(-207, -135)
# t.goto(-207, -129)
# t.goto(-207, -125)
# t.goto(-207, -120)
# t.goto(-203, -117)
# t.goto(-199, -115)
# t.goto(-194, -115)
# t.goto(-190, -116)
# t.goto(-185, -118)
# t.goto(-180, -123)
# t.goto(-177, -128)
# t.goto(-175, -135)
# t.goto(-175, -140)
# t.goto(-177, -145)
# t.end_fill()
# t.begin_fill()
# t.penup()
# t.goto(210, -237)
# t.pendown()
# t.goto(211, -230)
# t.goto(211, -224)
# t.goto(208, -216)
# t.goto(205, -214)
# t.goto(199, -213)
# t.goto(193, -214)
# t.goto(190, -215)
# t.goto(186, -218)
# t.goto(184, -220)
# t.goto(183, -225)
# t.goto(181, -231)
# t.goto(182, -235)
# t.goto(185, -239)
# t.goto(189, -243)
# t.goto(194, -244)
# t.goto(198, -244)
# t.goto(205, -243)
# t.goto(207, -240)
# t.goto(210, -237)
# t.end_fill()
# t.begin_fill()
# t.penup()
# t.goto(-4, -294)
# t.pendown()
# t.goto(1, -280)
# t.goto(4, -273)
# t.goto(8, -259)
# t.goto(12, -243)
# t.goto(10, -236)
# t.goto(4, -229)
# t.goto(-4, -225)
# t.goto(-18, -224)
# t.goto(-31, -223)
# t.goto(-42, -226)
# t.goto(-55, -229)
# t.goto(-73, -234)
# t.goto(-87, -240)
# t.goto(-99, -247)
# t.goto(-110, -259)
# t.goto(-119, -271)
# t.goto(-127, -282)
# t.goto(-130, -292)
# t.goto(-4, -294)
# t.end_fill()
# t.pencolor(60, 28, 28)
# t.fillcolor(60, 28, 28)
# t.begin_fill()
# t.penup()
# t.goto(-222, -128)
# t.pendown()
# t.goto(-222, -126)
# t.goto(-224, -123)
# t.goto(-232, -117)
# t.goto(-240, -115)
# t.goto(-244, -117)
# t.goto(-246, -120)
# t.goto(-246, -123)
# t.goto(-244, -126)
# t.goto(-240, -129)
# t.goto(-235, -130)
# t.goto(-230, -132)
# t.goto(-226, -131)
# t.goto(-222, -128)
# t.end_fill()
# t.begin_fill()
# t.penup()
# t.goto(-215, -103)
# t.pendown()
# t.goto(-216, -106)
# t.goto(-219, -109)
# t.goto(-223, -111)
# t.goto(-229, -110)
# t.goto(-231, -108)
# t.goto(-234, -105)
# t.goto(-239, -100)
# t.goto(-237, -97)
# t.goto(-235, -96)
# t.goto(-231, -95)
# t.goto(-228, -96)
# t.goto(-225, -98)
# t.goto(-215, -103)
# t.end_fill()
# t.begin_fill()
# t.penup()
# t.goto(-195, -98)
# t.pendown()
# t.goto(-197, -93)
# t.goto(-200, -87)
# t.goto(-203, -85)
# t.goto(-207, -83)
# t.goto(-209, -85)
# t.goto(-210, -88)
# t.goto(-210, -91)
# t.goto(-210, -95)
# t.goto(-208, -100)
# t.goto(-204, -102)
# t.goto(-201, -103)
# t.goto(-199, -102)
# t.goto(-197, -99)
# t.goto(-195, -98)
# t.end_fill()
# t.begin_fill()
# t.penup()
# t.goto(-172, -115)
# t.pendown()
# t.goto(-176, -114)
# t.goto(-178, -111)
# t.goto(-180, -109)
# t.goto(-182, -108)
# t.goto(-181, -104)
# t.goto(-180, -102)
# t.goto(-175, -99)
# t.goto(-172, -99)
# t.goto(-168, -100)
# t.goto(-166, -104)
# t.goto(-166, -109)
# t.goto(-169, -114)
# t.goto(-172, -115)
# t.end_fill()
# t.begin_fill()
# t.penup()
# t.goto(242, -236)
# t.pendown()
# t.goto(235, -238)
# t.goto(223, -240)
# t.goto(220, -238)
# t.goto(218, -236)
# t.goto(219, -231)
# t.goto(221, -229)
# t.goto(225, -226)
# t.goto(230, -224)
# t.goto(235, -223)
# t.goto(240, -224)
# t.goto(243, -228)
# t.goto(244, -229)
# t.goto(244, -234)
# t.goto(242, -236)
# t.end_fill()
# t.begin_fill()
# t.penup()
# t.goto(240, -207)
# t.pendown()
# t.goto(232, -217)
# t.goto(228, -218)
# t.goto(225, -218)
# t.goto(221, -216)
# t.goto(220, -213)
# t.goto(219, -209)
# t.goto(221, -205)
# t.goto(225, -202)
# t.goto(229, -198)
# t.goto(233, -196)
# t.goto(236, -196)
# t.goto(239, -197)
# t.goto(241, -200)
# t.goto(241, -204)
# t.goto(240, -207)
# t.end_fill()
# t.begin_fill()
# t.penup()
# t.goto(222, -197)
# t.pendown()
# t.goto(217, -201)
# t.goto(213, -203)
# t.goto(211, -202)
# t.goto(208, -200)
# t.goto(205, -197)
# t.goto(205, -193)
# t.goto(208, -189)
# t.goto(213, -184)
# t.goto(216, -182)
# t.goto(220, -180)
# t.goto(223, -180)
# t.goto(225, -181)
# t.goto(227, -183)
# t.goto(227, -186)
# t.goto(225, -191)
# t.goto(223, -193)
# t.goto(222, -197)
# t.end_fill()
# t.begin_fill()
# t.penup()
# t.goto(196, -201)
# t.pendown()
# t.goto(192, -207)
# t.goto(187, -207)
# t.goto(184, -207)
# t.goto(183, -204)
# t.goto(183, -199)
# t.goto(185, -193)
# t.goto(187, -191)
# t.goto(190, -190)
# t.goto(193, -190)
# t.goto(196, -191)
# t.goto(198, -194)
# t.goto(196, -199)
# t.goto(196, -201)
# t.end_fill()
# t.done()

# from turtle import *
#
#
# def nose(x,y):
#     """画鼻子"""
#     penup()
#     # 将海龟移动到指定的坐标
#     goto(x,y)
#     pendown()
#     # 设置海龟的方向（0-东、90-北、180-西、270-南）
#     setheading(-30)
#     begin_fill()
#     a = 0.4
#     for i in range(120):
#         if 0 <= i < 30 or 60 <= i <90:
#             a = a + 0.08
#             # 向左转3度
#             left(3)
#             # 向前走
#             forward(a)
#         else:
#             a = a - 0.08
#             left(3)
#             forward(a)
#     end_fill()
#     penup()
#     setheading(90)
#     forward(25)
#     setheading(0)
#     forward(10)
#     pendown()
#     # 设置画笔的颜色(红, 绿, 蓝)
#     pencolor(255, 155, 192)
#     setheading(10)
#     begin_fill()
#     circle(5)
#     color(160, 82, 45)
#     end_fill()
#     penup()
#     setheading(0)
#     forward(20)
#     pendown()
#     pencolor(255, 155, 192)
#     setheading(10)
#     begin_fill()
#     circle(5)
#     color(160, 82, 45)
#     end_fill()
#
#
# def head(x, y):
#     """画头"""
#     color((255, 155, 192), "pink")
#     penup()
#     goto(x,y)
#     setheading(0)
#     pendown()
#     begin_fill()
#     setheading(180)
#     circle(300, -30)
#     circle(100, -60)
#     circle(80, -100)
#     circle(150, -20)
#     circle(60, -95)
#     setheading(161)
#     circle(-300, 15)
#     penup()
#     goto(-100, 100)
#     pendown()
#     setheading(-30)
#     a = 0.4
#     for i in range(60):
#         if 0<= i < 30 or 60 <= i < 90:
#             a = a + 0.08
#             lt(3) #向左转3度
#             fd(a) #向前走a的步长
#         else:
#             a = a - 0.08
#             lt(3)
#             fd(a)
#     end_fill()
#
#
# def ears(x,y):
#     """画耳朵"""
#     color((255, 155, 192), "pink")
#     penup()
#     goto(x, y)
#     pendown()
#     begin_fill()
#     setheading(100)
#     circle(-50, 50)
#     circle(-10, 120)
#     circle(-50, 54)
#     end_fill()
#     penup()
#     setheading(90)
#     forward(-12)
#     setheading(0)
#     forward(30)
#     pendown()
#     begin_fill()
#     setheading(100)
#     circle(-50, 50)
#     circle(-10, 120)
#     circle(-50, 56)
#     end_fill()
#
#
# def eyes(x,y):
#     """画眼睛"""
#     color((255, 155, 192), "white")
#     penup()
#     setheading(90)
#     forward(-20)
#     setheading(0)
#     forward(-95)
#     pendown()
#     begin_fill()
#     circle(15)
#     end_fill()
#     color("black")
#     penup()
#     setheading(90)
#     forward(12)
#     setheading(0)
#     forward(-3)
#     pendown()
#     begin_fill()
#     circle(3)
#     end_fill()
#     color((255, 155, 192), "white")
#     penup()
#     seth(90)
#     forward(-25)
#     seth(0)
#     forward(40)
#     pendown()
#     begin_fill()
#     circle(15)
#     end_fill()
#     color("black")
#     penup()
#     setheading(90)
#     forward(12)
#     setheading(0)
#     forward(-3)
#     pendown()
#     begin_fill()
#     circle(3)
#     end_fill()
#
#
# def cheek(x,y):
#     """画脸颊"""
#     color((255, 155, 192))
#     penup()
#     goto(x,y)
#     pendown()
#     setheading(0)
#     begin_fill()
#     circle(30)
#     end_fill()
#
#
# def mouth(x,y):
#     """画嘴巴"""
#     color(239, 69, 19)
#     penup()
#     goto(x, y)
#     pendown()
#     setheading(-80)
#     circle(30, 40)
#     circle(40, 80)
#
#
# def setting():
#     """设置参数"""
#     pensize(4)
#     # 隐藏海龟
#     hideturtle()
#     colormode(255)
#     color((255, 155, 192), "pink")
#     setup(840, 500)
#     speed(10)
#
#
# def main():
#     """主函数"""
#     setting()
#     nose(-100, 100)
#     head(-69, 167)
#     ears(0, 160)
#     eyes(0, 140)
#     cheek(80, 10)
#     mouth(-20, 30)
#     done()
#
#
# if __name__ == '__main__':
#     main()

# value = float(input("请输入长度: "))
# unit = input("请输入单位: ")
# if unit == "in" or unit == "英寸":
#     print("%f英寸 = %f厘米" % (value, value * 2.54))
# elif unit == "cm" or unit == "厘米":
#     print("%f厘米 = %f英寸" % (value, value / 2.54))
# else:
#     print("你小子老实点！")

# a = float(input("请输入a的长度："))
# b = float(input("输入b的长度："))
# c = float(input("输入c的长度："))
#
# if a + b > c and a + c > b and b + c > a:
#     print("三角形的周长为：", a+b+c)
#     p = (a+b+c) / 2
#     area = (p*(p-a)*(p-b)*(p-c))**0.5
#     print("三角形的面积为：", area)


# sum = 0
# for i in range(1, 101, 2):
#     sum += i
# print(sum)

import random
answer = random.randint(1, 100)
while True:
    count = 0
    number = int(input("请输入您猜的数："))
    if number > answer:
        print("大啦")
    elif number < answer:
        print("小啦")
    else:
        print("不戳不戳！答对啦")
        break

